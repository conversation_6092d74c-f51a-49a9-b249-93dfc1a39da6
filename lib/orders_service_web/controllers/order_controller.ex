defmodule OrdersServiceWeb.OrderController do
  use OrdersServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params
  use OpenTelemetryDecorator

  alias Adyen.Services.Checkout
  alias OpenApiSpex.Reference
  alias OpenApiSpex.Schema
  alias OrdersService.Events.EventHelper
  alias OrdersService.Handler.PixelTrackingHandler
  alias OrdersService.InvitationOrder
  alias OrdersService.Order, as: OrderDB
  alias OrdersService.Orders.Order
  alias OrdersService.Orders.OrderHelper
  alias OrdersService.Orders.OrderItems.InitialOrderItem
  alias OrdersService.Orders.OrderProcessor
  alias OrdersService.Orders.OrderValidator
  alias OrdersService.Params.Order, as: OrderParams
  alias OrdersService.PayinTransaction
  alias OrdersService.Payments.Payment
  alias OrdersService.Repo
  alias OrdersService.Workers.OrderCreationWorker
  alias OrdersServiceWeb.ChangesetJSON
  alias OrdersServiceWeb.OpenAPI.PayOrderSchema
  alias OrdersServiceWeb.Plugs.Authorize
  alias OrdersServiceWeb.ValidationErrorJSON

  require Logger

  action_fallback OrdersServiceWeb.FallbackController

  tags ["Orders"]

  plug :conditional_authorize

  # Custom plug that skips authorization for status view (guest accessible)
  defp conditional_authorize(conn, _opts) do
    case {conn.private.phoenix_action, conn.params["view"]} do
      {:show, "status"} ->
        # Skip authorization for status view - accessible to guests
        conn

      {action, _} when action in [:index, :show] ->
        # Apply authorization for regular show and index actions
        Authorize.call(
          conn,
          Authorize.init(rule: ["promoter", "ticket", "read"], permission: "event.ticket.view")
        )

      _ ->
        # No authorization needed for other actions
        conn
    end
  end

  operation :index,
    summary: "List orders for an event",
    description: "List orders for an event with optional filters.",
    parameters: %{
      eventId: [
        in: :query,
        description: "Event ID",
        type: :string,
        example: "95ac7a4b-7b93-41bc-a7b1-818668fbabd0",
        required: false
      ],
      query: [
        in: :query,
        description: "Search query",
        type: :string,
        example: "John Doe",
        required: false
      ],
      ticketStatus: [
        in: :query,
        description: "Ticket status",
        schema: %Schema{
          type: :string,
          enum: ["ACTIVE", "DEFRAUDED", "REFUNDED", "REFUNDING", "USED", "UNUSED"]
        },
        example: "ACTIVE,UNUSED",
        required: false
      ],
      minSubmittedDate: [
        in: :query,
        description: "Minimum submitted date",
        type: :string,
        example: "2021-10-22T22:00:00.000Z",
        required: false
      ],
      maxSubmittedDate: [
        in: :query,
        description: "Maximum submitted date",
        type: :string,
        example: "2021-10-22T22:00:00.000Z",
        required: false
      ],
      minTotal: [
        in: :query,
        description: "Minimum total",
        type: :integer,
        example: 100,
        required: false
      ],
      maxTotal: [
        in: :query,
        description: "Maximum total",
        type: :integer,
        example: 200,
        required: false
      ],
      minOrderItemCount: [
        in: :query,
        description: "Minimum order item count",
        type: :integer,
        example: 1,
        required: false
      ],
      maxOrderItemCount: [
        in: :query,
        description: "Maximum order item count",
        type: :integer,
        example: 10,
        required: false
      ],
      refundedItems: [
        in: :query,
        description: "Show orders with refunded items",
        type: :boolean,
        example: true,
        required: false
      ],
      page: [
        in: :query,
        description: "Page number",
        type: :integer,
        example: 1,
        required: false
      ],
      pageSize: [
        in: :query,
        description: "Page size",
        type: :integer,
        example: 10,
        required: false
      ],
      sort: [
        in: :query,
        description: "Sort order",
        schema: %Schema{
          type: :string,
          enum: ["submittedDate", "name", "total", "orderItemCount"]
        },
        example: "submittedDate",
        required: false
      ],
      order: [
        in: :query,
        description: "Order",
        schema: %Schema{type: :string, enum: ["asc", "desc"]},
        example: "desc",
        required: false
      ]
    },
    responses: %{
      :ok => {"Order", "application/json", OrdersServiceWeb.Schemas.OrderListResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/401"},
      :forbidden => %Reference{"$ref": "#/components/responses/403"}
    }

  defparams(
    index_params(%{
      event_id: [field: Ecto.UUID],
      seller_id: [field: Ecto.UUID],
      query: [field: :string],
      ticket_status: [
        field: {:array, Ecto.Enum},
        values: [:ACTIVE, :DEFRAUDED, :REFUNDED, :REFUNDING, :USED, :UNUSED]
      ],
      min_submitted_date: [field: :utc_datetime],
      max_submitted_date: [field: :utc_datetime],
      min_total: [field: :integer],
      max_total: [field: :integer],
      min_order_item_count: [field: :integer],
      max_order_item_count: [field: :integer],
      refunded_items: [field: :boolean],
      page: [field: :integer],
      page_size: [field: :integer],
      sort: [field: Ecto.Enum, values: [:submitted_date, :name, :total, :order_item_count]],
      order: [field: Ecto.Enum, values: [:asc, :desc]]
    })
  )

  def index(conn, params) do
    ticket_status =
      value_list_from_params(params, "ticket_status", [
        "ACTIVE",
        "REFUNDED",
        "REFUNDING",
        "USED",
        "UNUSED"
      ])

    sort = params |> Map.get("sort", "submittedDate") |> ProperCase.snake_case()
    event_id = Map.get(params, "event_id")

    params =
      params
      |> Map.put("ticket_status", ticket_status)
      |> Map.put("sort", sort)

    auth_result =
      case Map.get(params, "seller_id") do
        nil ->
          event_id && event_authorized?(conn, event_id, "event.ticket.view")

        # This allows non sellers to also filter orders by the seller id
        seller_id ->
          current_seller?(conn, seller_id) ||
            (event_id && event_authorized?(conn, event_id, "event.ticket.view"))
      end

    with {_, true} <- {:auth, auth_result},
         {_, %{valid?: true} = changeset} <- {:params, index_params(params)} do
      orders_page =
        changeset
        |> Params.to_map()
        |> OrderDB.paginate_all()

      conn
      |> put_status(:ok)
      |> render(:index, page: orders_page)
    else
      {:auth, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Unauthorized access", error_code: :unauthorized})

      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)
    end
  end

  operation :show,
    summary: "Show order details or status",
    description:
      "Show details of an order or get minimal status information for polling. Requires the order ID and the event ID. Use view=status for lightweight polling.",
    parameters: %{
      id: [
        in: :query,
        description: "Order ID",
        type: :string,
        example: "fe05c7ce-9c57-4399-b8fb-b63398e06257",
        required: true
      ],
      event_id: [
        in: :query,
        description: "Event ID",
        type: :string,
        example: "95ac7a4b-7b93-41bc-a7b1-818668fbabd0",
        required: true
      ],
      view: [
        in: :query,
        description: "View type - use 'status' for minimal status information suitable for polling",
        type: :string,
        example: "status",
        required: false
      ]
    },
    responses: %{
      :ok => {"Order details or status", "application/json", OrdersServiceWeb.Schemas.OrderDetailsResponse},
      :unauthorized => %Reference{"$ref": "#/components/responses/401"},
      :forbidden => %Reference{"$ref": "#/components/responses/403"},
      :not_found => %Reference{"$ref": "#/components/responses/404"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/422"}
    }

  defparams(
    show_params(%{
      id!: [field: Ecto.UUID],
      event_id!: [field: Ecto.UUID]
    })
  )

  def show(conn, %{"id" => id, "event_id" => event_id, "view" => "status"} = params) do
    user_id = get_user_id(conn)

    with {_, %{valid?: true} = _changeset} <-
           {:params, show_params(params)},
         {_, %OrderDB{} = order} <- {:order, OrderDB.get_by_event_id(id, event_id, [:created_by_personal_information])},
         {_, true} <- {:access, user_matches_order?(user_id, order.created_by_personal_information)} do
      # Get payin transaction to check for 3D Secure action data
      payin_transaction = PayinTransaction.get_by_order_id(order.id)

      # Build response with optional action data for 3D Secure support in polling fallback
      response = %{
        id: order.id,
        status: order.status,
        created_at: order.inserted_at
      }

      # Add action data if available and order is in AWAITING_PAYMENT status (requires 3D Secure)
      response =
        case {order.status, payin_transaction} do
          {:AWAITING_PAYMENT, %PayinTransaction{psp_result: %{"action" => action}}} ->
            Map.put(response, :action, action)

          _ ->
            response
        end

      conn
      |> put_status(:ok)
      |> json(response)
    else
      {:params, changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:order, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Order not found", error_code: :not_found})

      {:access, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Unauthorized access", error_code: :forbidden})
    end
  end

  def show(conn, %{"id" => id, "event_id" => event_id} = params) do
    order_preloads = [
      :created_by_personal_information,
      :payin_transactions,
      :bill,
      :invitation_orders,
      billing_address: :country,
      order_tickets: :ticket
    ]

    seller_id = get_seller_id_from_token(conn)
    event_authorized? = event_authorized?(conn, event_id, "event.ticket.view")

    with {_, %{valid?: true} = _changeset} <-
           {:params, show_params(params)},
         {_, %OrderDB{} = order} <-
           {:order, OrderDB.get_by_event_id(id, event_id, order_preloads)},
         {_, true} <- {:access, access?(order, seller_id, event_authorized?)},
         {_, order_with_distribution_types} <- {:attach_distribution_types, attach_distribution_types_to_order(order)} do
      conn
      |> put_status(:ok)
      |> render(:show_details, order: order_with_distribution_types)
    else
      {:params, changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:order, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Order not found", error_code: :not_found})

      {:access, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Unauthorized access", error_code: :forbidden})

      {:attach_distribution_types, {:error, reason}} ->
        Logger.error("Failed to attach distribution types: #{inspect(reason)}")
        conn
        |> put_status(:ok)
        |> render(:show_details, order: order)
    end
  end

  operation :pay,
    summary: "Process payment for an order",
    description:
      "Validates and processes payment for the given order items. Can optionally include a 'receiptNumber' string field in the request body. Returns synchronous response (200 OK) by default, or asynchronous response (201 Created) if the request includes \"async\": true and the feature flag is enabled.",
    operation_id: "payOrder",
    request_body: {"Payment request body", "application/json", PayOrderSchema.PayOrderRequest},
    responses: %{
      :ok => {"Order", "application/json", PayOrderSchema.PayOrderResponse},
      :created => {"Async Order Processing", "application/json", PayOrderSchema.PayOrderAsyncResponse},
      :multi_status => {"Order", "application/json", PayOrderSchema.PayOrderMultiResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/unprocessable_entity"},
      :conflict => %Reference{"$ref": "#/components/responses/conflict"}
    }

  @decorate with_span("orders.pay", include: [:order_items])
  def pay(conn, %{"items" => _order_items} = order_params) do
    Logger.debug("Checkout tickets with #{inspect(order_params)}")
    order_params = Map.put(order_params, "userId", get_user_id(conn))
    seller_info = extract_seller_info(conn)
    order_params = prepare_order_params(order_params, seller_info)

    if Map.get(order_params, "async", "false") == "true" and Unleash.enabled?(:use_sse_for_orders) do
      Logger.debug("Using asynchronous order processing (request flag and feature enabled)")
      pay_async(conn, order_params)
    else
      Logger.debug("Using synchronous order processing (default)")
      pay_sync(conn, order_params)
    end
  end

  def pay(conn, order_params) do
    Logger.critical("Invalid request to pay order with params: #{inspect(order_params)}")

    conn
    |> put_status(:bad_request)
    |> json(%{
      data: %{
        status: 400,
        error: "Invalid request. Please provide valid order items and payment data."
      }
    })
  end

  operation :payments,
    summary: "Process payment for an order",
    description: "Validates and processes payment for the given order items.",
    operation_id: "payOrder",
    request_body: {"Payment request body", "application/json", PayOrderSchema.PayOrderRequest},
    responses: %{
      :ok => {"Order", "application/json", PayOrderSchema.PayOrderResponse},
      :created => {"Order", "application/json", PayOrderSchema.PayOrderCreatedResponse},
      :multi_status => {"Order", "application/json", PayOrderSchema.PayOrderMultiResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/unprocessable_entity"},
      :conflict => %Reference{"$ref": "#/components/responses/conflict"}
    }

  def payments(conn, %{"additionalData" => %{"stagedatesPayload" => order_params}} = params) do
    with {:ok, order_params} <- Jason.decode(order_params) do
      payment_data =
        params["payment"]["paymentMethod"] || params["paymentData"]["paymentMethod"]

      payment_method = payment_data["type"]

      order_params =
        order_params
        |> Map.put("paymentData", payment_data)
        |> Map.put("paymentMethod", payment_method)
        |> Map.put("psp", "adyen")

      pay(conn, order_params)
    end
  end

  def payments(conn, _params) do
    conn
    |> put_status(:unprocessable_entity)
    |> json(%{error: :invalid_params})
  end

  def create(
        conn,
        %{
          "invitation" => invitation,
          "userinfo" => userinfo,
          "email" => email,
          "amount" => amount,
          "user_id" => user_id
        } = order_params
      ) do
    Logger.debug("Create order for guestlist invitation #{inspect(invitation["id"])} and email #{inspect(email)}")

    attendees = order_params["attendees"]

    [api_token | _] = get_req_header(conn, "x-api-token")

    with {:service_token, {:ok, _claim}} <-
           {:service_token, ExServiceClient.Token.verify_and_validate(api_token)},
         {_, {:ok, created_order}} <-
           {:order, Order.create_with_invitation(user_id, userinfo, email, amount, attendees, invitation)} do
      conn
      |> put_status(:ok)
      |> json(%{"order_id" => created_order.id})
    else
      {:service_token, error} ->
        Logger.error("Service authentication failed because auf #{inspect(error)}")

        conn
        |> put_status(:forbidden)
        |> json(%{error: error})

      {:order, {:error, :personal_information_already_exists}} ->
        conn
        |> put_status(:conflict)
        |> json(%{error: :personal_information_already_exists})

      error ->
        Logger.critical("Can't create an order with invitation for because of #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error: :unknown_error})
    end
  end

  def create(conn, %{"items" => _order_items} = order_params) do
    # Use "old" function to create an order without invitation.
    # CleanUp names and parameters during the refactore SD1-1562
    pay(conn, order_params)
  end

  def create(conn, order_params) do
    payment_data = order_params["paymentMethod"]
    payment_method = payment_data["type"]

    order_params =
      order_params
      |> Map.put("paymentData", payment_data)
      |> Map.put("paymentMethod", payment_method)
      |> Map.put("psp", "adyen")

    pay(conn, order_params)
  end

  def pay_details(conn, params) do
    details =
      case params do
        %{"redirectResult" => %{} = redirect_result} -> redirect_result
        _ -> %{"details" => params}
      end

    payments_details(conn, details)
  end

  def payments_details(conn, params) do
    with {:ok, %{"merchantReference" => merchant_reference} = res} <-
           Checkout.payments_details(params),
         {:order, %OrderDB{email: email, id: order_id}} <-
           {:order, Repo.get(OrderDB, merchant_reference)} do
      # If redirect flow finished, notify the order creation job to unblock SSE
      case String.downcase(res["resultCode"] || "") do
        code when code in ["authorised", "authorized"] ->
          OrderCreationWorker.complete_job_from_webhook(order_id)

        code when code in ["refused", "cancelled", "canceled", "error"] ->
          # Let the SSE flow know about the failure immediately
          OrderCreationWorker.fail_job_from_webhook(order_id, "resultCode=#{res["resultCode"]}")

        _ ->
          :ok
      end

      conn
      |> put_status(:ok)
      |> json(%{email: email, orderId: order_id, resultCode: res["resultCode"]})
    else
      {:order, _} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: :order_not_found})

      error ->
        error
    end
  end

  operation :update,
    summary: "Updates an order and returns the updated order.",
    parameters: [
      id: [
        in: :path,
        description: "Order ID",
        type: :string,
        example: "fe05c7ce-9c57-4399-b8fb-b63398e06257"
      ]
    ],
    request_body: {"application/json", OrdersServiceWeb.OpenAPI.UpdateOrderRequest},
    responses: %{
      200 => {"Order", "application/json", OrdersServiceWeb.OpenAPI.UpdateOrderResponse},
      # The default responses have to be referenced here to show in int API documentatino
      400 => %Reference{"$ref": "#/components/responses/400"},
      403 => %Reference{"$ref": "#/components/responses/403"},
      422 => %Reference{"$ref": "#/components/responses/422"}
    }

  def update(conn, %{"order_id" => order_id, "invitation_id" => invitation_id, "invitation_status" => invitation_status}) do
    [api_token | _] = get_req_header(conn, "x-api-token")

    with {:service_token, {:ok, _claim}} <-
           {:service_token, ExServiceClient.Token.verify_and_validate(api_token)},
         order when not is_nil(order) <- OrderDB.get(order_id),
         {:check_order_invitation, true} <-
           {:check_order_invitation, InvitationOrder.exists?(order_id, invitation_id)} do
      case invitation_status do
        "ACCEPTED" ->
          OrderProcessor.process_pending_order(order)

        "REJECTED" ->
          OrderDB.set_status(order, :INVITATION_REJECTED, nil, invitation_id)
      end

      updated_order = OrderDB.get(order_id)

      conn
      |> put_status(200)
      |> render(:show, %{order: updated_order})
    else
      {:order, nil} ->
        conn
        |> put_status(404)
        |> json(%{error: :unknown_order_id})

      {:check_order_invitation, false} ->
        conn
        |> put_status(422)
        |> json(%{error: :oder_id_and_invitation_id_do_not_match})

      {:service_token, error} ->
        Logger.error("Service authentication failed because of #{inspect(error)}")

        conn
        |> put_status(403)
        |> json(%{error: error})
    end
  end

  def update(conn, %{"order_id" => order_id} = params) do
    Logger.warning("Try to update order #{inspect(order_id)} with params #{inspect(params)}")

    conn
    |> put_status(422)
    |> json(%{error: :no_valid_update_parameters})
  end

  def update(conn, params) do
    Logger.warning("Try to update an unknown order with prams #{inspect(params)}")

    conn
    |> put_status(400)
    |> json(%{error: :unknown_request})
  end

  operation :cleanup_pending,
    summary: "Clean up pending orders and set status to TIMEDOUT if necessary",
    request_body: {"url", "application/json"},
    responses: %{
      200 => {"count_pending_orders, count_updated, count_failed", "application/json", nil},
      # The default responses have to be referenced here to show in int API documentatino
      404 => %Reference{"$ref": "#/components/responses/404"}
    }

  def cleanup_pending(conn, _params) do
    execute_script(conn, %{
      "action" => "execute",
      "script_id" => "dce0489d-e090-4a49-9823-d2a8e1f1a1d2"
    })
  end

  operation :execute_script,
    summary: "Executes a script, identified by the script_id",
    request_body: {"url", "application/json"},
    responses: %{
      200 => {"script_response", "application/json", nil},
      # The default responses have to be referenced here to show in int API documentatino
      404 => %Reference{"$ref": "#/components/responses/404"}
    }

  def execute_script(conn, %{"action" => "execute", "script_id" => script_id} = _params) do
    case script_id do
      "dce0489d-e090-4a49-9823-d2a8e1f1a1d2" ->
        {:ok, response} = OrderHelper.cleanup_pending()
        conn |> put_status(200) |> json(response)

      _ ->
        conn |> put_status(404) |> json(%{error: :unknown_id})
    end
  end

  # Asynchronous order processing (SSE-based)
  defp pay_async(conn, order_params) do
    with {_, %{valid?: true}} <- {:params, OrderParams.changeset(ProperCase.to_snake_case(order_params))},
         {_, {:ok, job}} <- {:queue_job, OrderCreationWorker.queue_job(order_params)} do
      Logger.debug("Queued async order creation job ##{job.id}")

      conn
      |> put_status(:created)
      |> json(%{
        job_id: job.id,
        id: job.id,
        async: true,
        status: "processing"
      })
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          data: %{
            status: 400,
            errors: Ecto.Changeset.traverse_errors(changeset, &ChangesetJSON.translate_error/1)
          }
        })

      {:queue_job, {:error, error}} ->
        Logger.error("Failed to queue async order creation job: #{inspect(error)}")

        conn
        |> put_status(:bad_request)
        |> json(%{
          data: %{
            status: 400,
            error: "Failed to process order"
          }
        })
    end
  end

  # Synchronous order processing (original implementation)
  defp pay_sync(conn, %{"items" => order_items} = order_params) do
    payment_methods =
      case order_params["paymentData"] do
        %{"type" => type} -> [type]
        _ -> nil
      end

    user_roles = get_user_roles(conn)

    with {_, %{valid?: true}} <-
           {:params, OrderParams.changeset(ProperCase.to_snake_case(order_params))},
         {_, false} <- {:promoter_check, Enum.member?(user_roles, "PROMOTER")},
         {_, {:ok, events}} <-
           {:events, EventHelper.events_for_order_items(order_items)},
         {_, {:ok, order_items}} <-
           {:populate_order_items, InitialOrderItem.populate_all(events, order_items)},
         {_, :ok} <-
           {:validate, OrderValidator.validate(Map.put(order_params, "items", order_items))},
         {_, {:ok, %{order_data: order_data, order_items: order_items}}} <-
           {:order, Order.create(expand_order_params(order_params, order_items, payment_methods))},
         {_, {:ok, %{order: %{id: order_id, status: status} = order}}} when status in [:PENDING, :CREATED] <-
           {:pending, OrderProcessor.maybe_process_pending_order(Map.put(order_data, :events, events))},
         {_, {:ok, %{"resultCode" => result_code} = resp}, _order}
         when result_code not in ["Refused", "Cancelled"] <-
           {:adyen, Payment.make_payment(order_data, order_items, order_params, ip_from_conn(conn)), order} do
      user_agent = conn.req_headers |> List.keyfind("user-agent", 0) |> elem(1)
      ip = conn |> ip_from_conn() |> String.split(",") |> List.first()
      PixelTrackingHandler.handle_purchase_done(order_items, %{user_agent: user_agent, ip: ip})

      O11y.set_attributes(order_id: order_id, payment_method: get_in(order_params, ["paymentData", "type"]))

      conn
      |> put_status(:ok)
      |> json(resp |> Map.put(:reference_id, order_id) |> Map.put(:referenceId, order_id) |> Map.put(:async, false))
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          data: %{
            status: 400,
            errors: Ecto.Changeset.traverse_errors(changeset, &ChangesetJSON.translate_error/1)
          }
        })

      {:promoter_check, true} ->
        conn
        |> put_status(:forbidden)
        |> json(%{data: %{status: 403, error: "promoters are not allowed to buy tickets"}})

      {:pending, {:ok, %{order: %{id: order_id, status: :PAID}}}} ->
        conn
        |> put_status(:created)
        |> json(%{id: order_id, reference_id: order_id, referenceId: order_id})

      {:pending, {:partial_success, %{order: %{id: order_id, status: :PAID}}, error}} ->
        conn
        |> put_status(:multi_status)
        |> json(%{id: order_id, reference_id: order_id, referenceId: order_id, error: error})

      {:pending, {:error, :unauthorized_seller}} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          data: %{
            status: 403,
            error: "Seller does not have permission to sell tickets for this event"
          }
        })

      {:pending, {:error, :missing_event_id}} ->
        conn
        |> put_status(:bad_request)
        |> json(%{data: %{status: 400, error: "Could not determine event ID from order items"}})

      {:pending, {:error, :permission_check_failed}} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{data: %{status: 500, error: "Failed to verify seller permissions"}})

      {:pending, {:error, _msg} = error} ->
        Logger.critical("Something went wrong during payment: #{inspect(error)}")
        error

      {:adyen, {:ok, resp}, order} ->
        # idk either. This just copies the behaviour from the old code
        with {:ok, _order} <- OrderDB.set_status(order, :FAILED) do
          conn
          |> put_status(:ok)
          |> json(Map.put(resp, :reference_id, nil))
        end

      {:adyen, {:error, msg}, order} when is_map(msg) ->
        with {:ok, _order} <- OrderDB.set_status(order, :FAILED) do
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{data: %{status: 422, error: msg["message"] || msg}})
        end

      {:order, {:error, :blocked_email}} ->
        conn
        |> put_status(:forbidden)
        |> json(%{data: %{status: 403, error: "blocked email"}})

      {:order, {:error, :invalid_voucher}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{data: %{status: 422, error: "invalid voucher"}})

      {:order, {:error, :voucher_not_clearly_assignable}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{data: %{status: 422, error: "Voucher couldn't be uniquely applied for cart"}})

      {:order, {:error, :seats, :already_booked, _order}} ->
        conn
        |> put_status(:conflict)
        |> json(%{data: %{status: 422, error: "Seats already booked"}})

      {:events, {:error, :empty_order_items}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{data: %{status: 422, error: "empty order items"}})

      {:populate_order_items, error} ->
        Logger.critical("Error during payment while trying to populate order items: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          data: %{status: 500, error: "Failed to populate order items due to server error"}
        })

      {:validate, {:error, :cannot_get_ticket_quota}} ->
        # Logging is done in the validate functions
        # Response is without any internal details
        conn
        |> put_status(:internal_server_error)
        |> json(%{data: %{status: 500, error: "Internal Server Error"}})

      {:validate, {:error, :ticket_amount_not_available}} ->
        conn
        |> put_status(:conflict)
        |> json(%{data: %{status: 409, error: "Ticket amount is not available"}})

      {:validate, {:error, :variant_not_found}} ->
        conn
        |> put_status(:not_found)
        |> json(%{data: %{status: 404, error: "Variant not found"}})

      {:validate, {:error, errors}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> put_view(ValidationErrorJSON)
        |> render(:error, errors: errors)

      error ->
        Logger.critical("Unknown error during payment: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{data: %{status: 500, error: "Internal Server Error"}})
    end
  end

  defp ip_from_conn(conn) do
    case List.keyfind(conn.req_headers, "x-forwarded-for", 0) do
      {"x-forwarded-for", ip} -> ip
      _ -> conn.remote_ip |> :inet_parse.ntoa() |> to_string()
    end
  end

  defp extract_seller_info(%{assigns: %{user_payload: %{"affiliation" => sid, "email" => email} = payload}})
       when sid not in ["", nil],
       do: {sid, email, payload}

  defp extract_seller_info(_conn), do: {nil, nil, nil}

  defp prepare_order_params(order_params, {nil, nil, nil}), do: Map.delete(order_params, "seller_id")

  defp prepare_order_params(order_params, {seller_id, seller_email, user_payload}) do
    order_params
    |> Map.put("seller_id", seller_id)
    |> Map.put("seller_email", seller_email)
    |> Map.put("user_payload", user_payload)
  end

  defp access?(_order, nil, event_authorized?), do: event_authorized?

  defp access?(%OrderDB{seller_id: order_seller_id}, order_seller_id, _event_authorized?)
       when not is_nil(order_seller_id),
       do: true

  defp access?(_order, _seller_id, _event_authorized?), do: false

  # Helper function to check if user matches order creator
  # Handles both UUID and string-based user identifiers, and supports guest orders
  defp user_matches_order?(_user_id, personal_info) when is_nil(personal_info), do: false

  defp user_matches_order?(user_id, personal_info) do
    cond do
      # Both user and order are guests (nil user_id) - allow access
      is_nil(user_id) and is_nil(personal_info.user_id) ->
        true

      # User is guest but order has a user_id - deny access
      is_nil(user_id) ->
        false

      # User has ID, check if it matches order creator
      true ->
        case Ecto.UUID.cast(user_id) do
          {:ok, uuid} ->
            # User ID is a UUID, compare with user_id field
            personal_info.user_id == uuid

          _ ->
            # User ID is a string, compare with user_document_id field
            personal_info.user_document_id == user_id
        end
    end
  end

  defp expand_order_params(order_params, order_items, payment_methods) do
    order_params
    |> Map.put("items", order_items)
    |> Map.put("payment_methods", payment_methods)
  end

  defp attach_distribution_types_to_order(%OrderDB{order_tickets: order_tickets} = order) do
    try do
      # Get all unique distribution_type_ids for SALES_CHANNEL tickets
      sales_channel_ids =
        order_tickets
        |> Enum.filter(fn order_ticket ->
          order_ticket.ticket.distribution_type == :SALES_CHANNEL and
          not is_nil(order_ticket.ticket.distribution_type_id)
        end)
        |> Enum.map(fn order_ticket -> order_ticket.ticket.distribution_type_id end)
        |> Enum.uniq()

      # Fetch sales channels for these IDs
      sales_channels_map =
        sales_channel_ids
        |> Enum.reduce(%{}, fn sales_channel_id, acc ->
          case ExServiceClient.Services.EventsService.SalesChannel.get(sales_channel_id) do
            {:ok, sales_channel} -> Map.put(acc, sales_channel_id, sales_channel)
            {:error, _reason} -> acc
          end
        end)

      # Attach distribution type information to tickets
      updated_order_tickets =
        Enum.map(order_tickets, fn order_ticket ->
          updated_ticket = attach_distribution_type_to_ticket(order_ticket.ticket, sales_channels_map)
          %{order_ticket | ticket: updated_ticket}
        end)

      {:ok, %{order | order_tickets: updated_order_tickets}}
    rescue
      error ->
        Logger.error("Error attaching distribution types: #{inspect(error)}")
        {:error, error}
    end
  end

  defp attach_distribution_type_to_ticket(ticket, sales_channels_map) do
    case ticket.distribution_type do
      :SALES_CHANNEL when not is_nil(ticket.distribution_type_id) ->
        case Map.get(sales_channels_map, ticket.distribution_type_id) do
          nil ->
            # Sales channel not found, add empty distribution_type_info
            Map.put(ticket, :distribution_type_info, nil)
          sales_channel ->
            # Add sales channel information
            Map.put(ticket, :distribution_type_info, %{
              type: :SALES_CHANNEL,
              sales_channel: sales_channel
            })
        end
      _ ->
        # For REGULAR or other distribution types, add basic info
        Map.put(ticket, :distribution_type_info, %{
          type: ticket.distribution_type
        })
    end
  end
end
