defmodule OrdersServiceWeb.OrderJSONTest do
  use OrdersService.DataCase, async: true

  alias OrdersServiceWeb.OrderJSON
  alias OrdersService.Orders.OrderHelper

  import OrdersService.Factory

  describe "show_details/1" do
    test "includes tickets with distribution type information" do
      # Create tickets with different distribution types
      regular_ticket = build(:ticket, 
        distribution_type: :REGULAR, 
        distribution_type_id: nil,
        distribution_type_info: %{type: :REGULAR}
      )
      
      sales_channel_id = Faker.UUID.v4()
      sales_channel_ticket = build(:ticket, 
        distribution_type: :SALES_CHANNEL, 
        distribution_type_id: sales_channel_id,
        distribution_type_info: %{
          type: :SALES_CHANNEL,
          sales_channel: %{
            id: sales_channel_id,
            originalPrice: 1500,
            quotaMode: "RESERVED"
          }
        }
      )
      
      order_tickets = [
        build(:order_ticket, ticket: regular_ticket),
        build(:order_ticket, ticket: sales_channel_ticket)
      ]
      
      order = build(:order, 
        order_tickets: order_tickets,
        bill: build(:bill),
        created_by_personal_information: build(:personal_information),
        billing_address: build(:address),
        invitation_orders: [],
        payin_transactions: [build(:payin_transaction)]
      )

      result = OrderJSON.show_details(%{order: order})

      # Verify the basic structure
      assert result.id == order.id
      assert result.email == order.email
      assert result.status == order.status
      
      # Verify tickets are included
      assert is_list(result.tickets)
      assert length(result.tickets) == 2
      
      # Verify distribution type information is included
      [regular_ticket_json, sales_channel_ticket_json] = result.tickets
      
      # Check regular ticket
      assert regular_ticket_json.distributionType == :REGULAR
      assert regular_ticket_json.distributionTypeId == nil
      assert regular_ticket_json.distributionTypeInfo == %{type: :REGULAR}
      
      # Check sales channel ticket
      assert sales_channel_ticket_json.distributionType == :SALES_CHANNEL
      assert sales_channel_ticket_json.distributionTypeId == sales_channel_id
      assert sales_channel_ticket_json.distributionTypeInfo.type == :SALES_CHANNEL
      assert sales_channel_ticket_json.distributionTypeInfo.sales_channel.id == sales_channel_id
    end

    test "handles orders without tickets" do
      order = build(:order, 
        order_tickets: [],
        bill: build(:bill),
        created_by_personal_information: build(:personal_information),
        invitation_orders: [],
        payin_transactions: []
      )

      result = OrderJSON.show_details(%{order: order})

      assert result.tickets == []
    end

    test "handles orders with nil order_tickets" do
      order = build(:order, 
        order_tickets: nil,
        bill: build(:bill),
        created_by_personal_information: build(:personal_information),
        invitation_orders: [],
        payin_transactions: []
      )

      result = OrderJSON.show_details(%{order: order})

      assert result.tickets == nil
    end
  end
end
