defmodule OrdersService.Orders.OrderHelperTest do
  use OrdersService.DataCase, async: true

  import OrdersService.Factory

  alias OrdersService.Order
  alias OrdersService.Orders.OrderHelper
  alias OrdersService.OrderTicket
  alias OrdersService.Ticket

  describe "attach_distribution_types_to_order/1" do
    test "attaches distribution type info for REGULAR tickets" do
      ticket = build(:ticket, distribution_type: :REGULAR, distribution_type_id: nil)
      order_ticket = build(:order_ticket, ticket: ticket)
      order = build(:order, order_tickets: [order_ticket])

      assert {:ok, updated_order} = OrderHelper.attach_distribution_types_to_order(order)

      [updated_order_ticket] = updated_order.order_tickets
      assert updated_order_ticket.ticket.distribution_type_info == %{type: :REGULAR}
    end

    test "handles empty order_tickets list" do
      order = build(:order, order_tickets: [])

      assert {:ok, updated_order} = OrderHelper.attach_distribution_types_to_order(order)
      assert updated_order.order_tickets == []
    end

    test "handles GUEST_LIST_INVITATION distribution type" do
      invitation_id = Faker.UUID.v4()
      ticket = build(:ticket, distribution_type: :GUEST_LIST_INVITATION, distribution_type_id: invitation_id)
      order_ticket = build(:order_ticket, ticket: ticket)
      order = build(:order, order_tickets: [order_ticket])

      assert {:ok, updated_order} = OrderHelper.attach_distribution_types_to_order(order)

      [updated_order_ticket] = updated_order.order_tickets
      assert updated_order_ticket.ticket.distribution_type_info == %{type: :GUEST_LIST_INVITATION}
    end

    test "handles mixed distribution types in the same order" do
      regular_ticket = build(:ticket, distribution_type: :REGULAR, distribution_type_id: nil)

      invitation_ticket =
        build(:ticket, distribution_type: :GUEST_LIST_INVITATION, distribution_type_id: Faker.UUID.v4())

      order_tickets = [
        build(:order_ticket, ticket: regular_ticket),
        build(:order_ticket, ticket: invitation_ticket)
      ]

      order = build(:order, order_tickets: order_tickets)

      assert {:ok, updated_order} = OrderHelper.attach_distribution_types_to_order(order)

      [regular_order_ticket, invitation_order_ticket] = updated_order.order_tickets

      assert regular_order_ticket.ticket.distribution_type_info == %{type: :REGULAR}
      assert invitation_order_ticket.ticket.distribution_type_info == %{type: :GUEST_LIST_INVITATION}
    end
  end

  describe "extract_sales_channel_ids/1" do
    test "extracts unique sales channel IDs from order tickets" do
      sales_channel_id_1 = Faker.UUID.v4()
      sales_channel_id_2 = Faker.UUID.v4()

      order_tickets = [
        build(:order_ticket,
          ticket: build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id_1)
        ),
        build(:order_ticket, ticket: build(:ticket, distribution_type: :REGULAR, distribution_type_id: nil)),
        build(:order_ticket,
          ticket: build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id_2)
        ),
        # duplicate
        build(:order_ticket,
          ticket: build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id_1)
        )
      ]

      result = OrderHelper.extract_sales_channel_ids(order_tickets)

      assert length(result) == 2
      assert sales_channel_id_1 in result
      assert sales_channel_id_2 in result
    end

    test "returns empty list when no sales channel tickets" do
      order_tickets = [
        build(:order_ticket, ticket: build(:ticket, distribution_type: :REGULAR, distribution_type_id: nil)),
        build(:order_ticket,
          ticket: build(:ticket, distribution_type: :GUEST_LIST_INVITATION, distribution_type_id: Faker.UUID.v4())
        )
      ]

      result = OrderHelper.extract_sales_channel_ids(order_tickets)
      assert result == []
    end

    test "ignores SALES_CHANNEL tickets with nil distribution_type_id" do
      sales_channel_id = Faker.UUID.v4()

      order_tickets = [
        build(:order_ticket,
          ticket: build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id)
        ),
        build(:order_ticket, ticket: build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: nil))
      ]

      result = OrderHelper.extract_sales_channel_ids(order_tickets)

      assert length(result) == 1
      assert sales_channel_id in result
    end
  end

  describe "attach_distribution_type_to_ticket/2" do
    test "attaches sales channel info when available" do
      sales_channel_id = Faker.UUID.v4()
      mock_sales_channel = %{id: sales_channel_id, originalPrice: 1000}
      sales_channels_map = %{sales_channel_id => mock_sales_channel}

      ticket = build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id)

      result = OrderHelper.attach_distribution_type_to_ticket(ticket, sales_channels_map)

      assert result.distribution_type_info == %{
               type: :SALES_CHANNEL,
               sales_channel: mock_sales_channel
             }
    end

    test "handles missing sales channel gracefully" do
      sales_channel_id = Faker.UUID.v4()
      sales_channels_map = %{}

      ticket = build(:ticket, distribution_type: :SALES_CHANNEL, distribution_type_id: sales_channel_id)

      result = OrderHelper.attach_distribution_type_to_ticket(ticket, sales_channels_map)

      assert result.distribution_type_info == %{
               type: :SALES_CHANNEL,
               sales_channel: nil
             }
    end

    test "handles non-sales channel tickets" do
      ticket = build(:ticket, distribution_type: :REGULAR, distribution_type_id: nil)

      result = OrderHelper.attach_distribution_type_to_ticket(ticket, %{})

      assert result.distribution_type_info == %{type: :REGULAR}
    end
  end
end
